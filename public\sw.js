if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()}).then(()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(i,a)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let c={};const r=e=>n(e,t),u={module:{uri:t},exports:c,require:r};s[t]=Promise.all(i.map(e=>u[e]||r(e))).then(e=>(a(...e),c))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"4cfe9d91433944cf6142a4b146f14d9a"},{url:"/_next/static/chunks/1047-2f770ca6d5271037.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1283-6dcf46ee360a7ce0.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1636-069b70d8de46dda3.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1684-2e8a14b0e33c9ab9.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2185-c5708bc2eb98a161.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/2273-88d63ed225cf289a.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/2975-70e7db336d3a678b.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/3587-8a3438ba92b516e9.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/3664-c2f349dcbef441b7.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/3990-35d1c1d6aeb4ab41.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/4375.ab425c0e1eda2550.js",revision:"ab425c0e1eda2550"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/5304.2a273b66a53cf873.js",revision:"2a273b66a53cf873"},{url:"/_next/static/chunks/5508.4b534adbdb889dff.js",revision:"4b534adbdb889dff"},{url:"/_next/static/chunks/565-c6cf9e26e3ddbf75.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/6304-37f4d1e05fd19f8e.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/6766-b083812bfa8d91d7.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/7279-1436ce8c7f7cb4e6.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/740-ee7caaf7538d8b71.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/7655-369723c1a470d0dc.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/769.3da2bcf8764ae552.js",revision:"3da2bcf8764ae552"},{url:"/_next/static/chunks/8214-2525ba73624ab7f2.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/8317-2fe537b1963b64c2.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8881-0430871268202f73.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/8894.cc245b4c85bb3e0b.js",revision:"cc245b4c85bb3e0b"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9748-88df0698c2137452.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/9861.a8374c6efda1123f.js",revision:"a8374c6efda1123f"},{url:"/_next/static/chunks/app/(app)/dashboard/page-18bfb4abcc239c03.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/equipment/page-d0ebd6757b897ab3.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/layout-26d810a32bc867ab.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/maintenance/page-da84af3b7625b041.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-46fbccedce61685a.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-f8e263105d5ee52a.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/reports/page-b7816712550cabfe.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/transfers/page-2543c4cb77a6817f.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/(app)/users/page-b97b696ca8a51a60.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/layout-981a40158c2297f4.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/not-found-2b13751c226ac435.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/app/page-937255972c625467.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/main-app-ada3b357e3214a45.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/main-f0c49f4ebb047f5e.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-810c9d310d53adf4.js",revision:"zTT7jnAePmjzKG2-m_eiF"},{url:"/_next/static/css/f9a2ca0143741086.css",revision:"f9a2ca0143741086"},{url:"/_next/static/zTT7jnAePmjzKG2-m_eiF/_buildManifest.js",revision:"2f4508a9a028a5b36fb01832d3719a82"},{url:"/_next/static/zTT7jnAePmjzKG2-m_eiF/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:n,state:i})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
