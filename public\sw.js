if(!self.define){let e,s={};const t=(t,a)=>(t=new URL(t+".js",a).href,s[t]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=t,e.onload=s,document.head.appendChild(e)}else e=t,importScripts(t),s()}).then(()=>{let e=s[t];if(!e)throw new Error(`Module ${t} didn’t register its module`);return e}));self.define=(a,n)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(s[r])return;let i={};const c=e=>t(e,r),o={module:{uri:r},exports:i,require:c};s[r]=Promise.all(a.map(e=>o[e]||c(e))).then(e=>(n(...e),i))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"e63ab17ba460a6004b5f0ef93a7fc733"},{url:"/_next/static/chunks/1047-2f770ca6d5271037.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1283-6205f25e5a8e7845.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1481-274f4a1f10ddfdbe.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1570-35566d88bee5cfd5.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1608-783414185566f9b2.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1684-c4838a092f0e64e5.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2185-c5708bc2eb98a161.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/2960-16883bbedf559167.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/3664-c2f349dcbef441b7.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/3674-5b3d8f60242e1385.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/4197-4f9823a52cab8123.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/5304.2a273b66a53cf873.js",revision:"2a273b66a53cf873"},{url:"/_next/static/chunks/5508.4b534adbdb889dff.js",revision:"4b534adbdb889dff"},{url:"/_next/static/chunks/565-f24d6a0219cf9206.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/6099-b773e7d076d5d3bb.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/629-ec236aaf1d90055e.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/6534.44e6688812db5afa.js",revision:"44e6688812db5afa"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/6982-3ee42faa0c5c8ca0.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/7378-9d8d047c0fba69e5.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/7655-369723c1a470d0dc.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/769.3da2bcf8764ae552.js",revision:"3da2bcf8764ae552"},{url:"/_next/static/chunks/8038-494ddd3c2567fed5.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/8317-2fe537b1963b64c2.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8894.cc245b4c85bb3e0b.js",revision:"cc245b4c85bb3e0b"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9748-88df0698c2137452.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/9861.a8374c6efda1123f.js",revision:"a8374c6efda1123f"},{url:"/_next/static/chunks/app/(app)/dashboard/page-2c32431290deeebf.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/equipment/page-bf12a52cbf3d52aa.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/layout-fc8f08756d9159aa.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/maintenance/page-7082c0cb8c4947f1.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-3079654b313f935b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-4115c40b7db361d6.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/reports/page-21430e0122c1a258.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/test-realtime-sync/page-d3cf3eb2df547a39.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/transfers/page-a6e1df261e6a4424.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/(app)/users/page-2594e6062fe1ac80.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/debug-realtime/page-3dcbe0c6e216ad4b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/layout-88f037da70ac270b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/not-found-a1f8e156c24b57fe.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/page-7283406c897f30ac.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/test-auth/page-d4cd27aa4336e49c.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/test-clean-realtime/page-5d7f2bbd09503ca5.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/test-realtime/page-65192377e91d50ae.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/app/test-simple-realtime/page-050e4fef9c65a1f1.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/main-app-2a87d62be058a59c.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/main-b294c594aba7e79d.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-65abe40c4368f90b.js",revision:"foU9Y9kU_Dtrzr5XAAaXM"},{url:"/_next/static/css/592f10fcfc7e6bc7.css",revision:"592f10fcfc7e6bc7"},{url:"/_next/static/foU9Y9kU_Dtrzr5XAAaXM/_buildManifest.js",revision:"363196b0a65ccba98c97683a0ac79d12"},{url:"/_next/static/foU9Y9kU_Dtrzr5XAAaXM/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:t,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
