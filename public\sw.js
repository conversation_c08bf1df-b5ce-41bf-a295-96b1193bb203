if(!self.define){let s,e={};const i=(i,c)=>(i=new URL(i+".js",c).href,e[i]||new Promise(e=>{if("document"in self){const s=document.createElement("script");s.src=i,s.onload=e,document.head.appendChild(s)}else s=i,importScripts(i),e()}).then(()=>{let s=e[i];if(!s)throw new Error(`Module ${i} didn’t register its module`);return s}));self.define=(c,n)=>{const t=s||("document"in self?document.currentScript.src:"")||location.href;if(e[t])return;let a={};const r=s=>i(s,t),o={module:{uri:t},exports:a,require:r};e[t]=Promise.all(c.map(s=>o[s]||r(s))).then(s=>(n(...s),a))}}define(["./workbox-4754cb34"],function(s){"use strict";importScripts(),self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"482f94ebf7eebb11e237997c268bdf87"},{url:"/_next/static/chunks/1047-2f770ca6d5271037.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1283-6205f25e5a8e7845.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1481-274f4a1f10ddfdbe.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1570-35566d88bee5cfd5.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1608-783414185566f9b2.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1636-0d5e2be044cc7a4d.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1684-2e8a14b0e33c9ab9.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2185-c5708bc2eb98a161.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/2960-16883bbedf559167.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/3664-c2f349dcbef441b7.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/4197-4f9823a52cab8123.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/4375.ab425c0e1eda2550.js",revision:"ab425c0e1eda2550"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/5304.2a273b66a53cf873.js",revision:"2a273b66a53cf873"},{url:"/_next/static/chunks/5508.4b534adbdb889dff.js",revision:"4b534adbdb889dff"},{url:"/_next/static/chunks/565-f24d6a0219cf9206.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/629-e35248aa2308195d.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/6304-37f4d1e05fd19f8e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/6982-3ee42faa0c5c8ca0.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/7378-602c5f08f21fa0ed.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/7655-369723c1a470d0dc.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/769.3da2bcf8764ae552.js",revision:"3da2bcf8764ae552"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/8317-2fe537b1963b64c2.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8894.cc245b4c85bb3e0b.js",revision:"cc245b4c85bb3e0b"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9509-5bef0269cacbec9e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9748-88df0698c2137452.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/9861.a8374c6efda1123f.js",revision:"a8374c6efda1123f"},{url:"/_next/static/chunks/app/(app)/dashboard/page-024e28475803796d.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/equipment/page-28ea007bdb3fb6d8.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/layout-f4e88c4ba1a1a516.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/maintenance/page-4e21f018dcb67bfa.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-46fbccedce61685a.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-7b3b3ac5531b457b.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/reports/page-64d2c7d1f4031141.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/test-realtime-sync/page-a5f913a754384c48.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/transfers/page-d8bd72830640acc8.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/(app)/users/page-57f35f7aeebe8030.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/debug-realtime/page-00747f80b236436e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/layout-e060902cfc41dbc5.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/not-found-2b13751c226ac435.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/page-5eb3c72378ab968d.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/test-auth/page-ed491fa1ed5d7459.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/test-clean-realtime/page-7c78492f7b7f002b.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/test-realtime/page-5c1a2af3c10479af.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/app/test-simple-realtime/page-7018a257f26dea3e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/main-app-ada3b357e3214a45.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/main-f0c49f4ebb047f5e.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-836be2907a753de9.js",revision:"q7idK91yRVDJ3h8kohsRc"},{url:"/_next/static/css/d996ef0ab272f652.css",revision:"d996ef0ab272f652"},{url:"/_next/static/q7idK91yRVDJ3h8kohsRc/_buildManifest.js",revision:"363196b0a65ccba98c97683a0ac79d12"},{url:"/_next/static/q7idK91yRVDJ3h8kohsRc/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),s.cleanupOutdatedCaches(),s.registerRoute("/",new s.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:s,response:e,event:i,state:c})=>e&&"opaqueredirect"===e.type?new Response(e.body,{status:200,statusText:"OK",headers:e.headers}):e}]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new s.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new s.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new s.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new s.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/image\?url=.+$/i,new s.StaleWhileRevalidate({cacheName:"next-image",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp3|wav|ogg)$/i,new s.CacheFirst({cacheName:"static-audio-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp4)$/i,new s.CacheFirst({cacheName:"static-video-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:js)$/i,new s.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:css|less)$/i,new s.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new s.StaleWhileRevalidate({cacheName:"next-data",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:json|xml|csv)$/i,new s.NetworkFirst({cacheName:"static-data-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;const e=s.pathname;return!e.startsWith("/api/auth/")&&!!e.startsWith("/api/")},new s.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;return!s.pathname.startsWith("/api/")},new s.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>!(self.origin===s.origin),new s.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
