"use client"

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { usePageTransitions } from '@/hooks/use-page-transitions'

export function TransitionTestHelper() {
  const pathname = usePathname()
  const { isTransitioning, transitionClass, getTransitionConfig } = usePageTransitions()
  const [showDebug, setShowDebug] = useState(false)

  const testRoutes = [
    { path: '/dashboard', label: 'Dashboard', description: 'Fade transition' },
    { path: '/equipment', label: 'Equipment', description: 'Slide left' },
    { path: '/repair-requests', label: 'Repair Requests', description: 'Slide left' },
    { path: '/maintenance', label: 'Maintenance', description: 'Slide left' },
    { path: '/transfers', label: 'Transfers', description: 'Slide left' },
    { path: '/reports', label: 'Reports', description: 'Slide up' },
    { path: '/users', label: 'Users', description: 'Slide right' },
    { path: '/qr-scanner', label: 'QR Scanner', description: 'Slide up' },
  ]

  const currentConfig = getTransitionConfig(pathname)

  if (!showDebug) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowDebug(true)}
          className="bg-background/80 backdrop-blur-sm"
        >
          🧪 Test Transitions
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="bg-background/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Page Transition Tester</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDebug(false)}
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
          <CardDescription className="text-xs">
            Test page transitions by clicking the links below
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Current Status */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium">Current:</span>
              <Badge variant="outline" className="text-xs">
                {pathname}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium">Status:</span>
              <Badge variant={isTransitioning ? "default" : "secondary"} className="text-xs">
                {isTransitioning ? "Transitioning" : "Idle"}
              </Badge>
            </div>
            {transitionClass && (
              <div className="text-xs text-muted-foreground">
                Class: {transitionClass}
              </div>
            )}
            <div className="text-xs text-muted-foreground">
              Config: {currentConfig.direction} ({currentConfig.duration}ms)
            </div>
          </div>

          {/* Test Links */}
          <div className="space-y-1">
            <div className="text-xs font-medium mb-2">Test Routes:</div>
            <div className="grid gap-1 max-h-48 overflow-y-auto">
              {testRoutes.map((route) => (
                <Link
                  key={route.path}
                  href={route.path}
                  className={`block p-2 rounded text-xs hover:bg-muted transition-colors ${
                    pathname === route.path ? 'bg-muted' : ''
                  }`}
                >
                  <div className="font-medium">{route.label}</div>
                  <div className="text-muted-foreground">{route.description}</div>
                </Link>
              ))}
            </div>
          </div>

          {/* Performance Tips */}
          <div className="text-xs text-muted-foreground border-t pt-2">
            💡 <strong>Tips:</strong>
            <ul className="mt-1 space-y-1 ml-2">
              <li>• Open DevTools → Performance to monitor</li>
              <li>• Check GPU usage during transitions</li>
              <li>• Transitions are hardware accelerated</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
